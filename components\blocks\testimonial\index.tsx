"use client";

import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import Icon from "@/components/icon";
import { Section as SectionType, SectionItem } from "@/types/blocks/section";
import { useRef, useEffect, useState } from "react";

// 真人头像数据
const avatarUrls = [
  "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=face"
];

// 符合网站风格的评论卡片组件 - 四层遮罩背景
function TestimonialCard({ item }: { item: SectionItem }) {
  return (
    <Card className="testimonial-card select-none p-10 bg-black/35 backdrop-blur-sm border-white/20 rounded-3xl mb-6 w-full max-w-xs">
      <q className="leading-7 text-brand-light block mb-6">
        {item.description}
      </q>
      <div className="flex gap-4">
        <Avatar className="size-14 rounded-full flex-shrink-0">
          <AvatarImage
            src={item.image?.src}
            alt={item.image?.alt || item.title}
          />
        </Avatar>
        <div className="min-w-0 flex-1">
          <p className="font-semibold text-white">{item.title}</p>
          <p className="text-sm text-brand-light">
            {item.label}
          </p>
        </div>
      </div>
    </Card>
  );
}

// 单列组件 - 支持不同滚动速度和渐显渐隐效果
function TestimonialColumn({
  items,
  columnIndex,
  speedClass
}: {
  items: SectionItem[],
  columnIndex: number,
  speedClass: string
}) {
  const [isColumnPaused, setIsColumnPaused] = useState(false);

  return (
    <div
      onMouseEnter={() => setIsColumnPaused(true)}
      onMouseLeave={() => setIsColumnPaused(false)}
      className="overflow-hidden relative"
      style={{
        mask: 'linear-gradient(to bottom, transparent 0%, rgba(255,255,255,0.3) 60px, rgba(255,255,255,1) 120px, rgba(255,255,255,1) calc(100% - 120px), rgba(255,255,255,0.3) calc(100% - 60px), transparent 100%)',
        WebkitMask: 'linear-gradient(to bottom, transparent 0%, rgba(255,255,255,0.3) 60px, rgba(255,255,255,1) 120px, rgba(255,255,255,1) calc(100% - 120px), rgba(255,255,255,0.3) calc(100% - 60px), transparent 100%)'
      }}
    >
      <div
        className={`testimonial-column ${speedClass} ${isColumnPaused ? 'paused' : ''}`}
      >
        {items.map((item, index) => (
          <TestimonialCard key={`col-${columnIndex}-${index}`} item={item} />
        ))}
      </div>
    </div>
  );
}

export default function Testimonial({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  // 创建扩展的评论数据，包含真人头像
  const extendedItems = section.items?.map((item, index) => ({
    ...item,
    image: {
      ...item.image,
      src: avatarUrls[index % avatarUrls.length]
    }
  })) || [];

  // 将评论分配到不同的列中，每列独立循环
  const getColumnItems = (columnIndex: number, totalColumns: number) => {
    const columnItems = extendedItems.filter((_, index) => index % totalColumns === columnIndex);
    // 为每列创建足够的重复项以实现无缝滚动
    return [...columnItems, ...columnItems, ...columnItems, ...columnItems];
  };

  // 获取列的滚动速度类名
  const getSpeedClass = (columnIndex: number) => {
    const speedClasses = [
      'testimonial-column-speed-1', // 第一列：40秒
      'testimonial-column-speed-2', // 第二列：35秒（更快）
      'testimonial-column-speed-3'  // 第三列：45秒（更慢）
    ];
    return speedClasses[columnIndex] || 'testimonial-column-speed-1';
  };

  return (
    <section id={section.name} className="py-32">
      <div className="container">
        <div className="flex flex-col items-center justify-center max-w-[540px] mx-auto">
          {section.label && (
            <div className="flex items-center gap-1 text-sm font-semibold text-brand-dark mb-5">
              {section.icon && (
                <Icon name={section.icon} className="h-6 w-auto" />
              )}
              {section.label}
            </div>
          )}
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tighter text-center text-white drop-shadow-lg">
            {section.title}
          </h2>
          <p className="text-center mt-5 opacity-75 text-brand-dark">
            {section.description}
          </p>
        </div>

        {/* 3 列滚动布局 - 透明背景，四层遮罩卡片，不同滚动速度 */}
        <div className="mt-16 overflow-hidden h-[600px]">
          <div className="flex gap-6 justify-center">
            {[0, 1, 2].map((colIndex) => (
              <TestimonialColumn
                key={`col-${colIndex}`}
                items={getColumnItems(colIndex, 3)}
                columnIndex={colIndex}
                speedClass={getSpeedClass(colIndex)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
